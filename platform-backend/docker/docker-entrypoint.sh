#!/bin/sh

# Run composer install if the vendor directory does not exist
if [ ! -d "/var/www/vendor" ]; then
    echo "Vendor directory not found. Installing composer dependencies..."
    composer install --no-dev --optimize-autoloader --no-scripts
fi

# Run Laravel specific commands
php artisan key:generate --force
php artisan config:cache
php artisan route:cache

# Migrate PHPUnit configuration
if [ -f "/var/www/vendor/bin/phpunit" ]; then
    echo "Migrating PHPUnit configuration..."
    /var/www/vendor/bin/phpunit --migrate-configuration
else
    echo "PHPUnit binary not found. Ensure that PHPUnit is correctly installed."
    exit 1
fi

# Execute the original command passed to the container
exec "$@"
