<?php

namespace App\Console\Commands;

use App\Repositories\Eloquent\CurrencyRateRepository;
use Carbon\Carbon;
use Illuminate\Console\Command;
use DOMDocument;
use DOMXPath;

class ScrapeCurrencyRates extends Command
{
    protected $signature = 'scrape:currency-rates';
    protected $description = 'Scrape currency exchange rates from USD to EUR, NOK, CNY';

    protected $currencyRateRepository;

    public function __construct(CurrencyRateRepository $currencyRateRepository)
    {
        parent::__construct();
        $this->currencyRateRepository = $currencyRateRepository;
    }

    public function handle()
    {
        $urls = [
            'EUR' => 'https://www.xe.com/currencyconverter/convert/?Amount=1&From=USD&To=EUR',
            'NOK' => 'https://www.xe.com/currencyconverter/convert/?Amount=1&From=USD&To=NOK',
            'CNY' => 'https://www.xe.com/currencyconverter/convert/?Amount=1&From=USD&To=CNY',
        ];

        $rates = [];

        foreach ($urls as $currency => $url) {
            $html = $this->fetchHtml($url);

            if (!$html) {
                $this->error("Failed to fetch HTML for $currency.");
                continue;
            }

            $rate = $this->extractRate($html);

            if ($rate !== null) {
                $rates[$currency] = $rate;
                $this->info("Fetched rate for $currency: $rate");
            } else {
                $this->error("Failed to extract rate for $currency.");
            }
        }

        foreach ($rates as $currency => $rate) {
            $this->currencyRateRepository->updateOrCreate(
                [
                    'currency' => $currency,
                    'updated_date' => Carbon::now()->format('Y-m-d')
                ],
                ['rate' => $rate]
            );
        }

        $this->info('Currency rates updated successfully.');
    }

    private function fetchHtml(string $url): ?string
    {
        $ch = curl_init($url);
        curl_setopt_array($ch, [
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_USERAGENT => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)',
        ]);

        $html = curl_exec($ch);
        curl_close($ch);

        return $html ?: null;
    }

    private function extractRate(string $html): ?float
    {
        $segment = $this->extractConversionSegment($html);

        if (!$segment) {
            return null;
        }

        libxml_use_internal_errors(true);
        $dom = new DOMDocument();
        $dom->loadHTML($segment);
        libxml_clear_errors();

        $xpath = new DOMXPath($dom);
        $node = $xpath->query("//p[contains(text(), 'US Dollar')]/following-sibling::p[1]")?->item(0);

        if (!$node) {
            return null;
        }

        // Extract numeric value (e.g. "0.86925081 Euros" -> 0.86925081)
        $text = preg_replace('/[^0-9.]/', '', $node->nodeValue);
        return is_numeric($text) ? floatval($text) : null;
    }

    private function extractConversionSegment(string $html): string
    {
        $startTag = '<div data-testid="conversion" class="[grid-area:conversion]">';
        $endTag = 'conversion<!-- -->';

        $start = strpos($html, $startTag);
        if ($start === false) {
            return '';
        }

        $start += strlen($startTag);
        $end = strpos($html, $endTag, $start);

        return $end === false ? '' : substr($html, $start, $end - $start);
    }
}
