<?php

declare(strict_types=1);

namespace App\Console\Commands;

use App\Jobs\ScrapeBaseMarketRatesJob;
use App\Repositories\Eloquent\Contracts\BaseMarketIndexRateRepositoryInterface;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Symfony\Component\Console\Command\Command as CommandAlias;

class ScrapeBaseMarketRates extends Command
{
    protected $signature = 'scrape:base-market-rate';
    protected $description = 'Scrape and update the latest market rates values for the SOFR & EURIBOR base rates';

    public function __construct(private readonly BaseMarketIndexRateRepositoryInterface $baseMarketIndexRateRepository)
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle(): int
    {
        ScrapeBaseMarketRatesJob::dispatchSync($this->baseMarketIndexRateRepository);

        return CommandAlias::SUCCESS;
    }
}
