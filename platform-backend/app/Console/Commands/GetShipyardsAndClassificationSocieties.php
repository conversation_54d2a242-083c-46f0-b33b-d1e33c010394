<?php

declare(strict_types=1);

namespace App\Console\Commands;

use App\Models\ClassificationSociety;
use App\Models\Shipyard;
use Illuminate\Console\Command;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class GetShipyardsAndClassificationSocieties extends Command
{
    protected $signature = 'vessels:shipyards-and-societies';

    protected $description = 'Get all shipyards and classification societies';

    public function handle(): void
    {
        set_time_limit(0);
        $endpoint = Config::get('signal-ocean.api.endpoint');
        $apiKey = Config::get('signal-ocean.api.key');

        $page = 1;
        $pageSize = 100;
        $total = 58879;

        while ($page <= ($total / $pageSize)) {
            $vesselsEndpoint = "$endpoint/vessels-api/v2/vessels?page=$page&pageSize=$pageSize";
            $vesselsData = $this->fetchFromSignalOcean($vesselsEndpoint, [
                'Api-Key' => $apiKey
            ]);

            foreach ($vesselsData['Items'] as $vessel) {
                if (isset($vessel['ShipyardBuiltName'])) {
                    if (!Shipyard::where('name', $vessel['ShipyardBuiltName'])->exists()) {
                        Shipyard::create([
                            'name' => $vessel['ShipyardBuiltName'],
                            'tankers_rating' => 0,
                            'containers_rating' => 0,
                            'dry_bulks_rating' => 0,
                            'gas_rating' => 0,
                            'overall_rating' => 0,
                        ]);
                    }
                }

                if (isset($vessel['ClassificationRegister'])) {
                    if (!ClassificationSociety::where('name', $vessel['ClassificationRegister'])->exists()) {
                        ClassificationSociety::create([
                            'name' => $vessel['ClassificationRegister'],
                        ]);
                    }
                }
            }

            $page++;
        }
    }

    /**
     * @throws ConnectionException
     */
    private function fetchFromSignalOcean(string $endpoint, array $headers): ?array
    {
        $response = Http::withHeaders($headers)->get($endpoint);

        if ($response->successful()) {
            return $response->json();
        }

        Log::error("Failed to fetch data from Signal Ocean API", [
            'status_code' => $response->status(),
            'response_body' => $response->body(),
            'endpoint' => $endpoint,
        ]);

        return null;
    }
}
