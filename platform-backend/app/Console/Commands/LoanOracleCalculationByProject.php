<?php

namespace App\Console\Commands;

use App\Repositories\Eloquent\Contracts\VesselRepositoryInterface;
use App\Services\LoanOracle\Contracts\FacilityRequestServiceInterface;
use Illuminate\Console\Command;

class LoanOracleCalculationByProject extends Command
{
    protected $signature = 'loan-oracle:vessel';
    protected $description = 'Calculate loan oracle calculation by project';
    protected $vesselId = 24;

    public function __construct(protected readonly FacilityRequestServiceInterface $facilityRequestService, protected readonly VesselRepositoryInterface $vesselRepository)
    {
        parent::__construct();
    }

    public function handle()
    {
        $this->info(sprintf('Loan oracle calculation started for vesselID %s', $this->vesselId));

        $vessel = $this->vesselRepository->find($this->vesselId, ['shipyard', 'employments', 'vesselLoanOracle']);
        $this->facilityRequestService->createFacilityRequestByVessel($vessel);

        $this->info('Loan oracle calculation completed successfully.');
    }
}
