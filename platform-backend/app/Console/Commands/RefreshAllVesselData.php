<?php

namespace App\Console\Commands;

use App\Jobs\RefreshFleets;
use App\Models\Fleet;
use Illuminate\Console\Command;

class RefreshAllVesselData extends Command
{
    protected $signature = 'vessels:refresh-all-data';

    protected $description = 'Refresh all vessels, if applicable';

    public function handle()
    {
        $allFleets = Fleet::all();
        foreach ($allFleets as $fleet) {
            RefreshFleets::dispatch($fleet);
        }
    }
}
