<?php

namespace App\Console\Commands;

use App\Models\UserLogin;
use Illuminate\Console\Command;

class PurgeUserLoginCommand extends Command
{
    protected $signature = 'user-login:purge';

    protected $description = 'Purge user login logs older than the configured retention period.';

    public function handle(): int
    {
        if (!config('auth.clear_user_login', false)) return 0;

        $purgeDays = config('auth.purge', 365);

        $this->comment("Clearing user login logs older than {$purgeDays} days...");

        $deletedCount = UserLogin::where('login_at', '<', now()->subDays($purgeDays))->delete();

        $this->info("{$deletedCount} user login log(s) cleared.");

        return 0;
    }
}
