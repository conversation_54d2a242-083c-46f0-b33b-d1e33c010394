<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;
use Illuminate\Support\Facades\Log;

class Kernel extends ConsoleKernel
{
    /**
     * Define the application's command schedule.
     */
    protected function schedule(Schedule $schedule): void
    {
        // $schedule->command('inspire')->hourly();
        $schedule->command('scrape:currency-rates')->twiceDaily(10, 16);

        $scrapingDays = config('scraping.scraping_days', [2, 5]);
        if (!empty($scrapingDays)) {
            foreach ($scrapingDays as $day) {
                $schedule->command('scrape:base-market-rate')
                    ->weeklyOn($day, '00:00');
    }
        } else {
            Log::warning('No scraping days configured. scrape:base-market-rate command will not be scheduled.');
        }

        $schedule->command('vessels:refresh-all-data')->weekly();

        if (config('auth.clear_user_login')) {
            $schedule->command('user-login:purge')->daily();
        }
    }

    /**
     * Register the commands for the application.
     */
    protected function commands(): void
    {
        $this->load(__DIR__.'/Commands');

        require base_path('routes/console.php');
    }
}
