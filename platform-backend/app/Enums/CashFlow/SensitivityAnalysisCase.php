<?php

namespace App\Enums\CashFlow;

use App\Traits\Enums\HasLabel;

enum SensitivityAnalysisCase: string
{
    case BASE_CASE = 'Base Case';
    case BEST_CASE = 'Best Case';
    case DOWNSIDE_CASE = 'Downside Case';
    case UPSIDE_CASE = 'Upside Case';

    use <PERSON>Label;

    /**
     * Get the label for the enum value.
     *
     * @return string
     */
    public function label(): string
    {
        return match ($this) {
            self::BASE_CASE => 'Base Case',
            self::BEST_CASE => 'Best Case',
            self::DOWNSIDE_CASE => 'Downside Case',
            self::UPSIDE_CASE => 'Upside Case',
        };
    }
}
