<?php

namespace App\Enums\CashFlow;

use App\Enums\Contracts\SelectableInterface;
use App\Traits\Enums\HasLabel;

enum OpexType: string implements SelectableInterface
{
    use HasLabel;

    case TOTAL = 'total';
    case BREAKDOWN = 'breakdown';

    /**
     * Convert the enum to an array of options with id and name keys.
     *
     * @return array
     */
    public static function toOptionsArray(): array
    {
        return collect(self::cases())->map(function ($case) {
            return [
                'id' => $case->value,
                'name' => $case->label(),
            ];
        })->toArray();
    }

    /**
     * Get the label for the enum value.
     *
     * @return string
     */
    public function label(): string
    {
        return match ($this) {
            self::TOTAL => 'Total',
            self::BREAKDOWN => 'Breakdown',
        };
    }
}
