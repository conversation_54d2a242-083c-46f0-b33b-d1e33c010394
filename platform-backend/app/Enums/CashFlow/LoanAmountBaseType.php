<?php

namespace App\Enums\CashFlow;

use App\Enums\Contracts\SelectableInterface;
use App\Traits\Enums\HasLabel;

enum LoanAmountBaseType: string implements SelectableInterface
{
    use HasLabel;

    case FAIR_MARKET_VALUE = 'fair-market-value';
    case PURCHASE_PRICE = 'purchase-price';

    /**
     * Convert the enum to an array of options with id and name keys.
     *
     * @return array
     */
    public static function toOptionsArray(): array
    {
        return collect(self::cases())->map(function ($case) {
            return [
                'id' => $case->value,
                'name' => $case->label(),
            ];
        })->toArray();
    }

    /**
     * Get the label for the enum value.
     *
     * @return string
     */
    public function label(): string
    {
        return match ($this) {
            self::FAIR_MARKET_VALUE => 'Fair Market Value',
            self::PURCHASE_PRICE => 'Purchase Price',
        };
    }
}
